using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using ShiningCMusicApi.Services.Interfaces;
using ShiningCMusicApp.Pages;
using ShiningCMusicCommon.Models;
using Syncfusion.Blazor.Schedule;

namespace ShiningCMusicApi.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class TimesheetsController : ControllerBase
    {
        private readonly ITimesheetService _timesheetService;
        private readonly ILogger<TimesheetsController> _logger;

        public TimesheetsController(ITimesheetService timesheetService, ILogger<TimesheetsController> logger)
        {
            _timesheetService = timesheetService;
            _logger = logger;
        }

        // GET: api/timesheets
        [HttpGet]
        public async Task<ActionResult<IEnumerable<Timesheet>>> GetTimesheets()
        {
            try
            {
                var timesheets = await _timesheetService.GetTimesheetsAsync();
                return Ok(timesheets);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving timesheets");
                return StatusCode(500, "Internal server error");
            }
        }

        // GET: api/timesheets/tutor/{tutorId}
        [HttpGet("tutor/{tutorId}")]
        public async Task<ActionResult<IEnumerable<Timesheet>>> GetTimesheetsByTutor(int tutorId)
        {
            try
            {
                var timesheets = await _timesheetService.GetTimesheetsByTutorAsync(tutorId);
                return Ok(timesheets);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving timesheets for tutor {TutorId}", tutorId);
                return StatusCode(500, "Internal server error");
            }
        }

        // GET: api/timesheets/{id}
        [HttpGet("{id}")]
        public async Task<ActionResult<Timesheet>> GetTimesheet(int id)
        {
            try
            {
                var timesheet = await _timesheetService.GetTimesheetAsync(id);
                if (timesheet == null)
                {
                    return NotFound(new { message = "Timesheet not found" });
                }
                return Ok(timesheet);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving timesheet {Id}", id);
                return StatusCode(500, "Internal server error");
            }
        }

        // POST: api/timesheets
        [HttpPost]
        public async Task<ActionResult<Timesheet>> CreateTimesheet([FromBody] Timesheet timesheet)
        {
            try
            {
                if (timesheet.StudentId <= 0 || timesheet.TutorId <= 0 || timesheet.SubjectId <= 0)
                {
                    return BadRequest(new { message = "Student, Tutor, and Subject are required" });
                }

                if (timesheet.ClassDurationMinutes <= 0)
                {
                    return BadRequest(new { message = "Class duration must be greater than 0" });
                }

                timesheet.StartDate = timesheet.StartDate.ToUniversalTime();
                var createdTimesheet = await _timesheetService.CreateTimesheetAsync(timesheet);
                return CreatedAtAction(nameof(GetTimesheet), new { id = createdTimesheet.TimesheetId }, createdTimesheet);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating timesheet");
                return StatusCode(500, "Internal server error");
            }
        }

        // PUT: api/timesheets/{id}
        [HttpPut("{id}")]
        public async Task<IActionResult> UpdateTimesheet(int id, [FromBody] Timesheet timesheet)
        {
            try
            {
                if (timesheet.StudentId <= 0 || timesheet.TutorId <= 0 || timesheet.SubjectId <= 0)
                {
                    return BadRequest(new { message = "Student, Tutor, and Subject are required" });
                }

                if (timesheet.ClassDurationMinutes <= 0)
                {
                    return BadRequest(new { message = "Class duration must be greater than 0" });
                }

                timesheet.StartDate = timesheet.StartDate.ToUniversalTime();
                var success = await _timesheetService.UpdateTimesheetAsync(id, timesheet);
                if (success)
                {
                    return Ok(new { message = "Timesheet updated successfully" });
                }
                return NotFound(new { message = "Timesheet not found" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating timesheet {Id}", id);
                return StatusCode(500, "Internal server error");
            }
        }

        // DELETE: api/timesheets/{id}
        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteTimesheet(int id)
        {
            try
            {
                var success = await _timesheetService.DeleteTimesheetAsync(id);
                if (success)
                {
                    return Ok(new { message = "Timesheet deleted successfully" });
                }
                return NotFound(new { message = "Timesheet not found" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting timesheet {Id}", id);
                return StatusCode(500, "Internal server error");
            }
        }

        // GET: api/timesheets/{timesheetId}/entries
        [HttpGet("{timesheetId}/entries")]
        public async Task<ActionResult<IEnumerable<TimesheetEntry>>> GetTimesheetEntries(int timesheetId)
        {
            try
            {
                var entries = await _timesheetService.GetTimesheetEntriesAsync(timesheetId);
                return Ok(entries);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving timesheet entries for timesheet {TimesheetId}", timesheetId);
                return StatusCode(500, "Internal server error");
            }
        }

        // GET: api/timesheets/entries/{id}
        [HttpGet("entries/{id}")]
        public async Task<ActionResult<TimesheetEntry>> GetTimesheetEntry(int id)
        {
            try
            {
                var entry = await _timesheetService.GetTimesheetEntryAsync(id);
                if (entry == null)
                {
                    return NotFound(new { message = "Timesheet entry not found" });
                }
                return Ok(entry);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving timesheet entry {Id}", id);
                return StatusCode(500, "Internal server error");
            }
        }

        // POST: api/timesheets/entries
        [HttpPost("entries")]
        public async Task<ActionResult<TimesheetEntry>> CreateTimesheetEntry([FromBody] TimesheetEntry entry)
        {
            try
            {
                if (entry.TimesheetId <= 0)
                {
                    return BadRequest(new { message = "Timesheet ID is required" });
                }

                if (entry.AttendanceDateTime.HasValue)
                {
                    entry.AttendanceDateTime = entry.AttendanceDateTime.Value.ToUniversalTime();
                }
                var createdEntry = await _timesheetService.CreateTimesheetEntryAsync(entry);
                return CreatedAtAction(nameof(GetTimesheetEntry), new { id = createdEntry.TimesheetEntryId }, createdEntry);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating timesheet entry");
                return StatusCode(500, "Internal server error");
            }
        }

        // POST: api/timesheets/entries/multiple
        [HttpPost("entries/multiple")]
        public async Task<ActionResult<IEnumerable<TimesheetEntry>>> CreateMultipleTimesheetEntries([FromBody] CreateMultipleEntriesRequest request)
        {
            try
            {
                if (request.TimesheetId <= 0)
                {
                    return BadRequest(new { message = "Timesheet ID is required" });
                }

                if (request.NumberOfRecords <= 0 || request.NumberOfRecords > 50)
                {
                    return BadRequest(new { message = "Number of records must be between 1 and 50" });
                }

                var createdEntries = await _timesheetService.CreateMultipleTimesheetEntriesAsync(request.TimesheetId, request.NumberOfRecords);
                return Ok(createdEntries);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating multiple timesheet entries");
                return StatusCode(500, "Internal server error");
            }
        }

        // PUT: api/timesheets/entries/{id}
        [HttpPut("entries/{id}")]
        public async Task<IActionResult> UpdateTimesheetEntry(int id, [FromBody] TimesheetEntry entry)
        {
            try
            {
                if (entry.TimesheetId <= 0)
                {
                    return BadRequest(new { message = "Timesheet ID is required" });
                }

                if (entry.AttendanceDateTime.HasValue)
                {
                    entry.AttendanceDateTime = entry.AttendanceDateTime.Value.ToUniversalTime();
                }
                var success = await _timesheetService.UpdateTimesheetEntryAsync(id, entry);
                if (success)
                {
                    return Ok(new { message = "Timesheet entry updated successfully" });
                }
                return NotFound(new { message = "Timesheet entry not found" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating timesheet entry {Id}", id);
                return StatusCode(500, "Internal server error");
            }
        }

        // DELETE: api/timesheets/entries/{id}
        [HttpDelete("entries/{id}")]
        public async Task<IActionResult> DeleteTimesheetEntry(int id)
        {
            try
            {
                var success = await _timesheetService.DeleteTimesheetEntryAsync(id);
                if (success)
                {
                    return Ok(new { message = "Timesheet entry deleted successfully" });
                }
                return NotFound(new { message = "Timesheet entry not found" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting timesheet entry {Id}", id);
                return StatusCode(500, "Internal server error");
            }
        }
    }
}
