# TimesheetCleanupService Implementation

## Overview
This document describes the implementation of the TimesheetCleanupService, a background service that automatically deletes archived timesheets and their related entries after a specified retention period.

## ✅ IMPLEMENTATION COMPLETED

### What Was Implemented

**✅ Service Interface Updated:**
- Added `PermanentlyDeleteArchivedTimesheetsAsync(int olderThanDays)` to `ITimesheetService`
- Added `GetArchivedTimesheetsCountAsync(int olderThanDays)` to `ITimesheetService`

**✅ Service Implementation:**
- Implemented cleanup methods in `TimesheetService`
- Uses `UpdatedUTC` field to determine when timesheets were archived
- Only deletes records where `IsArchived = 1` AND `UpdatedUTC < DATEADD(day, -@OlderThanDays, GETUTCDATE())`
- **Deletes both timesheets AND their related timesheet entries** via database CASCADE DELETE
- Provides detailed logging showing counts of both timesheets and entries being deleted

**✅ Background Service:**
- `TimesheetCleanupService` - Runs every 24 hours (configurable)
- Default retention period: 90 days (configurable)
- Follows the same pattern as other cleanup services

**✅ Database Configuration:**
- Added configuration entries to Config table
- Supports enable/disable functionality
- Configurable interval and retention period

**✅ Registration:**
- Service registered in `Program.cs` as hosted service

### Key Features

#### 1. **Configurable Retention Period**
- **Default**: 90 days (longer than other cleanup services)
- **Configurable via**: Database config, environment variables, or appsettings.json
- **Configuration key**: `TimesheetCleanupRetentionDays`

#### 2. **Configurable Execution Interval**
- **Default**: 24 hours
- **Configurable via**: Database config, environment variables, or appsettings.json
- **Configuration key**: `TimesheetCleanupIntervalHours`

#### 3. **Enable/Disable Control**
- **Default**: Enabled
- **Configurable via**: Database config
- **Configuration key**: `TimesheetCleanupEnabled`

#### 4. **Safe Deletion Logic**
- Only deletes timesheets where `IsArchived = 1` (soft-deleted records)
- Only deletes timesheets that have been updated/archived (`UpdatedUTC IS NOT NULL`)
- Only deletes timesheets older than the retention period
- **Automatically deletes ALL related TimesheetEntry records** via database CASCADE DELETE constraint
- Provides pre-deletion counts for both timesheets and entries for audit logging

#### 5. **Comprehensive Logging**
- Startup and shutdown logging
- Configuration loading logging
- Cleanup execution logging with counts
- Error handling and logging

## Database Configuration

### Configuration Entries
The following configurations are added to the `Config` table with `GroupId = 200` (BackgroundProcessors):

| Key | Default Value | Data Type | Description |
|-----|---------------|-----------|-------------|
| `TimesheetCleanupEnabled` | `true` | `bool` | Enable/disable timesheet cleanup service |
| `TimesheetCleanupIntervalHours` | `24` | `int` | Timesheet cleanup interval in hours |
| `TimesheetCleanupRetentionDays` | `90` | `int` | Timesheet cleanup retention period in days |

### SQL Scripts Provided

1. **`Add_TimesheetCleanup_Config.sql`** - Standalone script to add configurations
2. **`Update_Config_Add_TimesheetCleanup.sql`** - Safe update script for existing databases
3. **`Verify_TimesheetCleanup_Config.sql`** - Verification script to check configuration setup
4. **`Add_Config_Table.sql`** - Updated to include TimesheetCleanup configs by default

## Configuration Hierarchy

The service uses a three-tier configuration hierarchy (in order of precedence):

1. **Database Configuration** (highest priority)
   - `Config` table with `GroupId = 200`
   - Can be managed via admin interface

2. **Environment Variables** (medium priority)
   - `TIMESHEET_CLEANUP_ENABLED`
   - `TIMESHEET_CLEANUP_INTERVAL_HOURS`
   - `TIMESHEET_CLEANUP_RETENTION_DAYS`

3. **appsettings.json** (lowest priority)
   - `TimesheetCleanup:Enabled`
   - `TimesheetCleanup:IntervalHours`
   - `TimesheetCleanup:RetentionDays`

## How It Works

### Archiving Process
1. When a timesheet is "deleted" via the UI, it is soft-deleted:
   - `IsArchived` is set to `1` (true)
   - `UpdatedUTC` is set to current UTC time
2. The timesheet remains in the database but is excluded from normal queries

### Cleanup Process
1. Background service runs every 24 hours (configurable)
2. Service queries for timesheets where:
   - `IsArchived = 1` (archived timesheets only)
   - `UpdatedUTC IS NOT NULL` (has been updated/archived)
   - `UpdatedUTC < DATEADD(day, -@OlderThanDays, GETUTCDATE())` (older than retention period)
3. Service counts both timesheets and timesheet entries that will be deleted (for logging)
4. Matching timesheets are permanently deleted from the database
5. **ALL related TimesheetEntry records are automatically deleted** via CASCADE DELETE constraint
6. Detailed logging shows exact counts of both timesheets and entries deleted

### Database Schema Support
The existing database schema already supports this cleanup:
- `Timesheets` table has `IsArchived` and `UpdatedUTC` columns
- `TimesheetEntries` table has foreign key with `ON DELETE CASCADE`
- No schema changes required

## Deployment Instructions

### For New Deployments
1. The configurations are included in `Add_Config_Table.sql`
2. No additional steps required

### For Existing Deployments
1. Run `Update_Config_Add_TimesheetCleanup.sql` to add configurations
2. Optionally run `Verify_TimesheetCleanup_Config.sql` to verify setup
3. Restart the API application to load the new service

### Verification
1. Check application logs for "TimesheetCleanupService initialized" message
2. Use the verification script to check database configuration
3. Monitor logs for cleanup execution (runs every 24 hours by default)

## Monitoring and Management

### Log Messages to Monitor
- `TimesheetCleanupService initialized`
- `TimesheetCleanupService configuration loaded`
- `Found {TimesheetCount} archived timesheets and {EntryCount} timesheet entries older than {Days} days to delete`
- `Successfully deleted {TimesheetCount} archived timesheets and {EntryCount} timesheet entries older than {Days} days`
- `No archived timesheets older than {Days} days found for cleanup`

### Configuration Management
- Use the admin interface to modify configurations in the `Config` table
- Changes take effect on the next cleanup cycle (no restart required)
- Set `TimesheetCleanupEnabled` to `false` to disable the service

### API Endpoints
- `GET /api/config/background-processors/TimesheetCleanup/enabled` - Check if service is enabled
- `GET /api/config/200/TimesheetCleanupRetentionDays` - Get retention period
- `PUT /api/config/{configId}` - Update configuration values

## Security and Safety

### Safety Features
- Only deletes archived records (`IsArchived = 1`)
- Requires `UpdatedUTC` to be set (prevents accidental deletion of active records)
- Uses parameterized queries to prevent SQL injection
- Comprehensive error handling and logging

### Backup Recommendations
- Ensure regular database backups before cleanup runs
- Consider implementing a backup/archive process for deleted records if audit trail is required
- Monitor cleanup counts to detect unusual deletion patterns

## Performance Considerations

### Database Impact
- Cleanup runs during low-usage hours (configurable)
- Uses efficient SQL queries with proper indexing
- CASCADE DELETE handles related records automatically

### Recommended Indexes
The following indexes should exist for optimal performance:
```sql
-- On Timesheets table
CREATE INDEX IX_Timesheets_IsArchived_UpdatedUTC 
ON Timesheets (IsArchived, UpdatedUTC);

-- On TimesheetEntries table (for CASCADE DELETE performance)
CREATE INDEX IX_TimesheetEntries_TimesheetId 
ON TimesheetEntries (TimesheetId);
```

## Troubleshooting

### Common Issues
1. **Service not running**: Check if `TimesheetCleanupEnabled` is set to `true`
2. **No deletions occurring**: Verify retention period and check if any timesheets meet criteria
3. **Configuration not loading**: Check database connection and Config table structure

### Debug Steps
1. Check application startup logs for initialization messages
2. Verify database configurations using the verification script
3. Monitor logs during cleanup execution time
4. Check for any error messages in application logs

This implementation provides a robust, configurable, and safe way to automatically clean up archived timesheet data while maintaining system performance and data integrity.
