using ShiningCMusicApp.Services.Interfaces;
using ShiningCMusicCommon.Models;
using System.Net.Http.Headers;
using System.Net.Http.Json;
using System.Text;
using System.Text.Json;

namespace ShiningCMusicApp.Services.Implementations
{
    public class ConfigApiService : IConfigApiService
    {
        private readonly HttpClient _httpClient;
        private readonly ILogger<ConfigApiService> _logger;
        private readonly IAuthenticationService _authService;
        private readonly string _baseUrl;
        private readonly JsonSerializerOptions _jsonOptions;

        public ConfigApiService(HttpClient httpClient, ILogger<ConfigApiService> logger, IAuthenticationService authService, ApiConfiguration apiConfig)
        {
            _httpClient = httpClient;
            _logger = logger;
            _authService = authService;
            _baseUrl = apiConfig.BaseUrl;
            _jsonOptions = new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase
            };
        }

        private async Task<bool> SetAuthorizationHeaderAsync()
        {
            var token = await _authService.GetAccessTokenAsync();
            if (!string.IsNullOrEmpty(token))
            {
                _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);
                return true;
            }
            return false;
        }

        public async Task<IEnumerable<Config>> GetAllConfigsAsync()
        {
            try
            {
                if (!await SetAuthorizationHeaderAsync())
                {
                    return new List<Config>();
                }

                var response = await _httpClient.GetAsync($"{_baseUrl}/config");
                response.EnsureSuccessStatusCode();
                
                var configs = await response.Content.ReadFromJsonAsync<IEnumerable<Config>>(_jsonOptions);
                return configs ?? new List<Config>();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving all configurations");
                throw;
            }
        }

        public async Task<IEnumerable<ConfigGroup>> GetConfigGroupsAsync()
        {
            try
            {
                if (!await SetAuthorizationHeaderAsync())
                {
                    throw new UnauthorizedAccessException("Unable to get authentication token for API access");
                }

                var url = $"{_baseUrl.TrimEnd('/')}/config/groups";
                _logger.LogInformation($"Requesting config groups from: {url}");

                var response = await _httpClient.GetAsync(url);
                response.EnsureSuccessStatusCode();

                var groups = await response.Content.ReadFromJsonAsync<IEnumerable<ConfigGroup>>(_jsonOptions);
                return groups ?? new List<ConfigGroup>();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving configuration groups");
                throw;
            }
        }

        public async Task<ConfigGroup?> GetConfigGroupAsync(int groupId)
        {
            try
            {
                if (!await SetAuthorizationHeaderAsync())
                {
                    return null;
                }

                var response = await _httpClient.GetAsync($"{_baseUrl}/config/groups/{groupId}");
                if (response.StatusCode == System.Net.HttpStatusCode.NotFound)
                {
                    return null;
                }
                
                response.EnsureSuccessStatusCode();
                return await response.Content.ReadFromJsonAsync<ConfigGroup>(_jsonOptions);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving configuration group {GroupId}", groupId);
                throw;
            }
        }

        public async Task<Config?> GetConfigAsync(int groupId, string key)
        {
            try
            {
                if (!await SetAuthorizationHeaderAsync())
                {
                    return null;
                }

                var response = await _httpClient.GetAsync($"{_baseUrl}/config/{groupId}/{key}");
                if (response.StatusCode == System.Net.HttpStatusCode.NotFound)
                {
                    return null;
                }
                
                response.EnsureSuccessStatusCode();
                return await response.Content.ReadFromJsonAsync<Config>(_jsonOptions);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving configuration {GroupId}:{Key}", groupId, key);
                throw;
            }
        }

        public async Task<string?> GetConfigValueAsync(int groupId, string key)
        {
            var config = await GetConfigAsync(groupId, key);
            return config?.Value;
        }

        public async Task<T?> GetConfigValueAsync<T>(int groupId, string key, T? defaultValue = default)
        {
            try
            {
                var value = await GetConfigValueAsync(groupId, key);
                if (string.IsNullOrEmpty(value))
                {
                    return defaultValue;
                }

                if (typeof(T) == typeof(string))
                {
                    return (T)(object)value;
                }
                else if (typeof(T) == typeof(int) || typeof(T) == typeof(int?))
                {
                    if (int.TryParse(value, out var intValue))
                    {
                        return (T)(object)intValue;
                    }
                }
                else if (typeof(T) == typeof(bool) || typeof(T) == typeof(bool?))
                {
                    if (bool.TryParse(value, out var boolValue))
                    {
                        return (T)(object)boolValue;
                    }
                }
                else if (typeof(T) == typeof(double) || typeof(T) == typeof(double?))
                {
                    if (double.TryParse(value, out var doubleValue))
                    {
                        return (T)(object)doubleValue;
                    }
                }

                return defaultValue;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error parsing configuration value {GroupId}:{Key} to type {Type}", groupId, key, typeof(T).Name);
                return defaultValue;
            }
        }

        public async Task<bool> UpdateConfigAsync(int configId, string value, bool isEnabled)
        {
            try
            {
                if (!await SetAuthorizationHeaderAsync())
                {
                    return false;
                }

                var updateRequest = new { Value = value, IsEnabled = isEnabled };
                var json = JsonSerializer.Serialize(updateRequest, _jsonOptions);
                var content = new StringContent(json, Encoding.UTF8, "application/json");
                
                var response = await _httpClient.PutAsync($"{_baseUrl}/config/{configId}", content);
                return response.IsSuccessStatusCode;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating configuration {ConfigId}", configId);
                return false;
            }
        }

        public async Task<bool> UpdateConfigAsync(int groupId, string key, string value, bool isEnabled = true)
        {
            try
            {
                if (!await SetAuthorizationHeaderAsync())
                {
                    return false;
                }

                var updateRequest = new { GroupId = groupId, Key = key, Value = value, IsEnabled = isEnabled };
                var json = JsonSerializer.Serialize(updateRequest, _jsonOptions);
                var content = new StringContent(json, Encoding.UTF8, "application/json");
                
                var response = await _httpClient.PutAsync($"{_baseUrl}/config", content);
                return response.IsSuccessStatusCode;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating configuration {GroupId}:{Key}", groupId, key);
                return false;
            }
        }

        public async Task<bool> UpdateConfigsAsync(IEnumerable<ConfigUpdateRequest> updates)
        {
            try
            {
                if (!await SetAuthorizationHeaderAsync())
                {
                    return false;
                }

                var json = JsonSerializer.Serialize(updates, _jsonOptions);
                var content = new StringContent(json, Encoding.UTF8, "application/json");
                
                var response = await _httpClient.PutAsync($"{_baseUrl}/config/batch", content);
                return response.IsSuccessStatusCode;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating multiple configurations");
                return false;
            }
        }

        public async Task<Config?> CreateConfigAsync(Config config)
        {
            try
            {
                if (!await SetAuthorizationHeaderAsync())
                {
                    return null;
                }

                var response = await _httpClient.PostAsJsonAsync($"{_baseUrl}/config", config, _jsonOptions);
                response.EnsureSuccessStatusCode();
                
                var createdConfig = await response.Content.ReadFromJsonAsync<Config>(_jsonOptions);
                return createdConfig ?? throw new InvalidOperationException("Failed to deserialize created config");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating configuration");
                throw;
            }
        }

        public async Task<bool> DeleteConfigAsync(int configId)
        {
            try
            {
                if (!await SetAuthorizationHeaderAsync())
                {
                    return false;
                }

                var response = await _httpClient.DeleteAsync($"{_baseUrl}/config/{configId}");
                return response.IsSuccessStatusCode;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting configuration {ConfigId}", configId);
                return false;
            }
        }


    }
}
