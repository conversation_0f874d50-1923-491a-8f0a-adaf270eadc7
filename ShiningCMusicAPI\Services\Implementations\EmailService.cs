using System.Net;
using System.Net.Mail;
using ShiningCMusicApi.Services.Interfaces;
using ShiningCMusicCommon.Models;

namespace ShiningCMusicApi.Services.Implementations
{
    public class EmailService : IEmailService
    {
        private readonly IConfiguration _configuration;
        private readonly ILogger<EmailService> _logger;
        private readonly IEmailTemplateService _emailTemplateService;
        private readonly IConfigService _configService;

        public EmailService(IConfiguration configuration, ILogger<EmailService> logger, IEmailTemplateService emailTemplateService, IConfigService configService)
        {
            _configuration = configuration;
            _logger = logger;
            _emailTemplateService = emailTemplateService;
            _configService = configService;
        }

        public async Task<bool> SendScheduleReadyEmailAsync(string tutorEmail, string tutorName)
        {
            if (string.IsNullOrWhiteSpace(tutorEmail))
            {
                _logger.LogWarning("Cannot send email: tutor email is empty");
                return false;
            }

            var placeholders = new Dictionary<string, string>
            {
                { "TutorName", tutorName }
            };

            return await SendEmailFromTemplateAsync("ScheduleReady", tutorEmail, placeholders);
        }

        public async Task<bool> SendPaymentReminderEmailAsync(string studentEmail, string studentName, int lessonsRemaining, string paymentDeadline)
        {
            if (string.IsNullOrWhiteSpace(studentEmail))
            {
                _logger.LogWarning("Cannot send payment reminder email: student email is empty");
                return false;
            }

            var placeholders = new Dictionary<string, string>
            {
                { "StudentName", studentName },
                { "LessonsRemaining", lessonsRemaining.ToString() },
                { "PaymentDeadline", paymentDeadline }
            };

            return await SendEmailFromTemplateAsync("PaymentReminder", studentEmail, placeholders);
        }

        public async Task<bool> SendEmailFromTemplateAsync(string templateName, string toEmail, Dictionary<string, string>? placeholders = null)
        {
            try
            {
                var template = await _emailTemplateService.GetTemplateAsync(templateName);
                if (template == null)
                {
                    _logger.LogError("Email template '{TemplateName}' not found", templateName);
                    return false;
                }

                var subject = ReplacePlaceholders(template.Subject ?? "", placeholders);
                var body = ReplacePlaceholders(template.BodyHtml ?? template.BodyText ?? "", placeholders);
                var isHtml = !string.IsNullOrWhiteSpace(template.BodyHtml);

                return await SendEmailAsync(toEmail, subject, body, isHtml, template.CcEmailAddresses, template.BccEmailAddresses, template.Attachments);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to send email from template '{TemplateName}' to {Email}", templateName, toEmail);
                return false;
            }
        }

        public async Task<bool> SendEmailAsync(string toEmail, string subject, string body, bool isHtml = true)
        {
            return await SendEmailAsync(toEmail, subject, body, isHtml, null, null, null);
        }

        private async Task<bool> SendEmailAsync(string toEmail, string subject, string body, bool isHtml, string? ccEmails, string? bccEmails, ICollection<ShiningCMusicCommon.Models.EmailAttachment>? attachments)
        {
            try
            {
                // Try to read from database first, then environment variables, then configuration
                var smtpServer = await _configService.GetConfigValueAsync((int)ConfigGroupId.Email, "SmtpServer")
                    ?? Environment.GetEnvironmentVariable("EMAIL_SMTP_SERVER")
                    ?? _configuration["EmailSettings:SmtpServer"];

                var smtpPortStr = await _configService.GetConfigValueAsync((int)ConfigGroupId.Email, "SmtpPort")
                    ?? Environment.GetEnvironmentVariable("EMAIL_SMTP_PORT")
                    ?? _configuration["EmailSettings:SmtpPort"] ?? "587";
                var smtpPort = int.Parse(smtpPortStr);

                var senderEmail = await _configService.GetConfigValueAsync((int)ConfigGroupId.Email, "SenderEmail")
                    ?? Environment.GetEnvironmentVariable("EMAIL_SENDER_EMAIL")
                    ?? _configuration["EmailSettings:SenderEmail"];

                var senderPassword = await _configService.GetConfigValueAsync((int)ConfigGroupId.Email, "SenderPassword")
                    ?? Environment.GetEnvironmentVariable("EMAIL_SENDER_PASSWORD")
                    ?? _configuration["EmailSettings:SenderPassword"];

                var senderName = await _configService.GetConfigValueAsync((int)ConfigGroupId.Email, "SenderName")
                    ?? Environment.GetEnvironmentVariable("EMAIL_SENDER_NAME")
                    ?? _configuration["EmailSettings:SenderName"];

                if (string.IsNullOrWhiteSpace(smtpServer) || string.IsNullOrWhiteSpace(senderEmail) || string.IsNullOrWhiteSpace(senderPassword))
                {
                    _logger.LogError("Email configuration is incomplete");
                    return false;
                }

                using var client = new SmtpClient(smtpServer, smtpPort)
                {
                    Credentials = new NetworkCredential(senderEmail, senderPassword),
                    EnableSsl = true
                };

                var mailMessage = new MailMessage
                {
                    From = new MailAddress(senderEmail, senderName),
                    Subject = subject,
                    Body = body,
                    IsBodyHtml = isHtml
                };

                mailMessage.To.Add(toEmail);

                // Add CC recipients
                if (!string.IsNullOrWhiteSpace(ccEmails))
                {
                    var ccAddresses = ccEmails.Split(';', StringSplitOptions.RemoveEmptyEntries);
                    foreach (var cc in ccAddresses)
                    {
                        if (!string.IsNullOrWhiteSpace(cc.Trim()))
                        {
                            mailMessage.CC.Add(cc.Trim());
                        }
                    }
                }

                // Add BCC recipients
                if (!string.IsNullOrWhiteSpace(bccEmails))
                {
                    var bccAddresses = bccEmails.Split(';', StringSplitOptions.RemoveEmptyEntries);
                    foreach (var bcc in bccAddresses)
                    {
                        if (!string.IsNullOrWhiteSpace(bcc.Trim()))
                        {
                            mailMessage.Bcc.Add(bcc.Trim());
                        }
                    }
                }

                // Add attachments
                if (attachments != null)
                {
                    foreach (var attachment in attachments)
                    {
                        if (File.Exists(attachment.AttachmentPath))
                        {
                            var mailAttachment = new Attachment(attachment.AttachmentPath)
                            {
                                Name = attachment.AttachmentName
                            };
                            mailMessage.Attachments.Add(mailAttachment);
                        }
                        else
                        {
                            _logger.LogWarning("Attachment file not found: {AttachmentPath}", attachment.AttachmentPath);
                        }
                    }
                }

                await client.SendMailAsync(mailMessage);
                _logger.LogInformation("Email sent successfully to {Email} using database configuration", toEmail);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to send email to {Email}", toEmail);
                return false;
            }
        }

        private static string ReplacePlaceholders(string content, Dictionary<string, string>? placeholders)
        {
            if (string.IsNullOrWhiteSpace(content) || placeholders == null || placeholders.Count == 0)
            {
                return content;
            }

            var result = content;
            foreach (var placeholder in placeholders)
            {
                result = result.Replace($"{{{placeholder.Key}}}", placeholder.Value);
            }

            return result;
        }
    }
}
