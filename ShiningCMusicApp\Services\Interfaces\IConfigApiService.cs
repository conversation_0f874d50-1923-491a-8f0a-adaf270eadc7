using ShiningCMusicCommon.Models;

namespace ShiningCMusicApp.Services.Interfaces
{
    public interface IConfigApiService
    {
        Task<IEnumerable<Config>> GetAllConfigsAsync();
        Task<IEnumerable<ConfigGroup>> GetConfigGroupsAsync();
        Task<ConfigGroup?> GetConfigGroupAsync(int groupId);
        Task<Config?> GetConfigAsync(int groupId, string key);
        Task<string?> GetConfigValueAsync(int groupId, string key);
        Task<T?> GetConfigValueAsync<T>(int groupId, string key, T? defaultValue = default);
        Task<bool> UpdateConfigAsync(int configId, string value, bool isEnabled);
        Task<bool> UpdateConfigAsync(int groupId, string key, string value, bool isEnabled = true);
        Task<bool> UpdateConfigsAsync(IEnumerable<ConfigUpdateRequest> updates);
        Task<Config?> CreateConfigAsync(Config config);
        Task<bool> DeleteConfigAsync(int configId);
    }
}
