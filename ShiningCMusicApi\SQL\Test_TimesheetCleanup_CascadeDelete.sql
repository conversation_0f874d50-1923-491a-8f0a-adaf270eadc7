USE [MusicSchool]
GO

-- Test script to verify that TimesheetCleanup properly deletes both timesheets and their entries
-- This script creates test data, performs cleanup, and verifies the results

PRINT 'Testing TimesheetCleanup CASCADE DELETE functionality...';
PRINT '';

-- Step 1: Create test timesheet (archived and old enough to be deleted)
DECLARE @TestTimesheetId INT;
DECLARE @TestStudentId INT = (SELECT TOP 1 StudentId FROM Students);
DECLARE @TestTutorId INT = (SELECT TOP 1 TutorId FROM Tutors);
DECLARE @TestSubjectId INT = (SELECT TOP 1 SubjectId FROM Subjects);

-- Only proceed if we have required test data
IF @TestStudentId IS NULL OR @TestTutorId IS NULL OR @TestSubjectId IS NULL
BEGIN
    PRINT '❌ Cannot run test: Missing required test data (Students, Tutors, or Subjects)';
    PRINT 'Please ensure the database has at least one Student, Tutor, and Subject record.';
    RETURN;
END

-- Create a test timesheet that's archived and older than 90 days
INSERT INTO Timesheets (StudentId, TutorId, SubjectId, StartDate, ContactNumber, ClassDurationMinutes, Notes, CreatedUTC, UpdatedUTC, IsArchived)
VALUES (
    @TestStudentId,
    @TestTutorId, 
    @TestSubjectId,
    DATEADD(day, -100, GETUTCDATE()), -- 100 days ago
    '555-TEST-123',
    60,
    'Test timesheet for cleanup verification',
    DATEADD(day, -100, GETUTCDATE()), -- Created 100 days ago
    DATEADD(day, -95, GETUTCDATE()),  -- Updated/Archived 95 days ago (older than 90-day retention)
    1 -- IsArchived = true
);

SET @TestTimesheetId = SCOPE_IDENTITY();
PRINT CONCAT('✓ Created test timesheet with ID: ', @TestTimesheetId);

-- Step 2: Create test timesheet entries
INSERT INTO TimesheetEntries (TimesheetId, AttendanceDateTime, Signature, IsPresent, Notes, CreatedUTC, UpdatedUTC)
VALUES 
    (@TestTimesheetId, DATEADD(day, -99, GETUTCDATE()), 'Test Signature 1', 1, 'Test entry 1', DATEADD(day, -99, GETUTCDATE()), NULL),
    (@TestTimesheetId, DATEADD(day, -98, GETUTCDATE()), 'Test Signature 2', 1, 'Test entry 2', DATEADD(day, -98, GETUTCDATE()), NULL),
    (@TestTimesheetId, DATEADD(day, -97, GETUTCDATE()), 'Test Signature 3', 0, 'Test entry 3', DATEADD(day, -97, GETUTCDATE()), NULL);

DECLARE @TestEntriesCreated INT = @@ROWCOUNT;
PRINT CONCAT('✓ Created ', @TestEntriesCreated, ' test timesheet entries');

-- Step 3: Verify test data exists
DECLARE @TimesheetExists INT = (SELECT COUNT(*) FROM Timesheets WHERE TimesheetId = @TestTimesheetId);
DECLARE @EntriesExist INT = (SELECT COUNT(*) FROM TimesheetEntries WHERE TimesheetId = @TestTimesheetId);

PRINT '';
PRINT 'Before cleanup:';
PRINT CONCAT('- Test timesheet exists: ', CASE WHEN @TimesheetExists > 0 THEN 'YES' ELSE 'NO' END);
PRINT CONCAT('- Test entries exist: ', @EntriesExist, ' entries');

-- Step 4: Perform the cleanup (simulate what the service does)
PRINT '';
PRINT 'Performing cleanup (deleting archived timesheets older than 90 days)...';

-- Count what will be deleted (including our test data)
DECLARE @TimesheetsToDelete INT = (
    SELECT COUNT(*)
    FROM Timesheets
    WHERE IsArchived = 1
    AND UpdatedUTC IS NOT NULL
    AND UpdatedUTC < DATEADD(day, -90, GETUTCDATE())
);

DECLARE @EntriesToDelete INT = (
    SELECT COUNT(te.TimesheetEntryId)
    FROM TimesheetEntries te
    INNER JOIN Timesheets t ON te.TimesheetId = t.TimesheetId
    WHERE t.IsArchived = 1
    AND t.UpdatedUTC IS NOT NULL
    AND t.UpdatedUTC < DATEADD(day, -90, GETUTCDATE())
);

PRINT CONCAT('- Timesheets to delete: ', @TimesheetsToDelete);
PRINT CONCAT('- Entries to delete: ', @EntriesToDelete);

-- Perform the actual deletion (same SQL as the service uses)
DELETE FROM Timesheets
WHERE IsArchived = 1
AND UpdatedUTC IS NOT NULL
AND UpdatedUTC < DATEADD(day, -90, GETUTCDATE());

DECLARE @TimesheetsDeleted INT = @@ROWCOUNT;
PRINT CONCAT('✓ Deleted ', @TimesheetsDeleted, ' timesheets');

-- Step 5: Verify CASCADE DELETE worked
DECLARE @TimesheetExistsAfter INT = (SELECT COUNT(*) FROM Timesheets WHERE TimesheetId = @TestTimesheetId);
DECLARE @EntriesExistAfter INT = (SELECT COUNT(*) FROM TimesheetEntries WHERE TimesheetId = @TestTimesheetId);

PRINT '';
PRINT 'After cleanup:';
PRINT CONCAT('- Test timesheet exists: ', CASE WHEN @TimesheetExistsAfter > 0 THEN 'YES' ELSE 'NO' END);
PRINT CONCAT('- Test entries exist: ', @EntriesExistAfter, ' entries');

-- Step 6: Verify results
PRINT '';
PRINT 'Test Results:';
PRINT '=============';

IF @TimesheetExistsAfter = 0 AND @EntriesExistAfter = 0
BEGIN
    PRINT '✅ SUCCESS: Both timesheet and entries were deleted correctly';
    PRINT '✅ CASCADE DELETE is working properly';
END
ELSE
BEGIN
    PRINT '❌ FAILURE: CASCADE DELETE did not work as expected';
    IF @TimesheetExistsAfter > 0
        PRINT '   - Timesheet still exists (should have been deleted)';
    IF @EntriesExistAfter > 0
        PRINT CONCAT('   - ', @EntriesExistAfter, ' entries still exist (should have been deleted)');
END

-- Step 7: Verify foreign key constraint exists
PRINT '';
PRINT 'Verifying CASCADE DELETE constraint:';
SELECT 
    fk.name AS 'Constraint Name',
    OBJECT_NAME(fk.parent_object_id) AS 'Child Table',
    OBJECT_NAME(fk.referenced_object_id) AS 'Parent Table',
    fk.delete_referential_action_desc AS 'Delete Action'
FROM sys.foreign_keys fk
WHERE fk.name = 'FK_TimesheetEntries_Timesheets';

IF EXISTS (SELECT 1 FROM sys.foreign_keys WHERE name = 'FK_TimesheetEntries_Timesheets' AND delete_referential_action_desc = 'CASCADE')
BEGIN
    PRINT '✅ CASCADE DELETE constraint is properly configured';
END
ELSE
BEGIN
    PRINT '❌ CASCADE DELETE constraint is missing or incorrectly configured';
    PRINT 'Please run the Add_Timesheet_Tables.sql script to create the proper constraint';
END

PRINT '';
PRINT 'TimesheetCleanup CASCADE DELETE test completed.';
GO
